/* Math Editor Dialog Styles */
.matheditor-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Arial, sans-serif;
}

.matheditor-content {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 1200px;
    min-width: 1000px;
    max-height: 90vh;
    min-height: 600px;
    overflow: hidden;
    padding: 0 0 10px 0;
    position: relative;
    display: flex;
    flex-direction: column;
}

.matheditor-container {
    padding: 0 20px;
    margin-top: 10px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.matheditor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    border-bottom: 1px solid #e1e1e1;
    padding: 16px 24px;
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    color: #323130;
    height: 60px;
    border-radius: 4px 4px 0 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.matheditor-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #323130;
    display: flex;
    align-items: center;
    gap: 8px;
}

.matheditor-header h2::before {
    content: "∑";
    font-size: 20px;
    color: #106ebe;
    font-weight: bold;
}

.matheditor-close-btn {
    background: transparent;
    border: 1px solid transparent;
    font-size: 16px;
    cursor: pointer;
    color: #605e5c;
    padding: 4px;
    line-height: 1;
    border-radius: 3px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.matheditor-close-btn:hover {
    background: rgba(16, 110, 190, 0.1);
    border-color: rgba(16, 110, 190, 0.3);
    color: #106ebe;
}

.matheditor-input-container {
    margin-bottom: 16px;
    margin-top: 6px;
    padding: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.matheditor-label {
    display: block;
    margin-bottom: 4px;
    font-weight: bold;
    color: #555;
    font-size: 12px;
    flex-shrink: 0;
}

#matheditor-input {
    width: 100%;
    font-size: 16px;
    border: 2px solid #2196f3;
    border-radius: 4px;
    min-height: 120px;
    flex: 1;
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.15);
    background-color: #f9f9f9;
    transition: all 0.3s;
    margin-bottom: 10px;
    resize: none;
}

/* Modern Microsoft Word 365 Style Toolbar Tabs */
.matheditor-toolbar-tabs {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 0;
    border-bottom: 1px solid #e1e1e1;
    padding: 0;
    background: #ffffff;
    border-radius: 0;
    overflow-x: auto;
    overflow-y: hidden;
    height: 32px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
    position: relative;
    z-index: 1;
    flex-shrink: 0;
}

.matheditor-toolbar-tabs::-webkit-scrollbar {
    height: 3px;
}

.matheditor-toolbar-tabs::-webkit-scrollbar-thumb {
    background-color: rgba(0, 105, 180, 0.3);
    border-radius: 10px;
}

.matheditor-tab-btn {
    padding: 0 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
    margin: 0;
    color: #323130;
    position: relative;
    height: 32px;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 0;
}

.matheditor-tab-btn:hover {
    color: #106ebe;
    background: rgba(16, 110, 190, 0.1);
}

.matheditor-tab-btn:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: transparent;
    transition: all 0.2s ease;
}

.matheditor-tab-btn:hover:after {
    background: rgba(16, 110, 190, 0.3);
}

.matheditor-tab-btn.active {
    background: transparent;
    color: #106ebe;
    font-weight: 600;
    z-index: 2;
}

.matheditor-tab-btn.active:after {
    background: #106ebe;
}

/* Perfect Word 365 Style Ribbon Toolbar */
.matheditor-toolbar {
    display: block;
    margin-bottom: 16px;
    padding: 12px 20px;
    background: linear-gradient(to bottom, #ffffff, #fafafa);
    border: none;
    border-top: none;
    border-radius: 0;
    height: auto;
    min-height: 110px;
    max-height: 120px;
    overflow-x: hidden;
    overflow-y: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
    position: relative;
    z-index: 1;
    width: 100%;
}

.matheditor-tab-content {
    display: none;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 0;
    align-items: flex-start;
    justify-content: space-evenly;
    height: 86px;
    overflow-x: hidden;
    overflow-y: hidden;
    white-space: nowrap;
    width: 100%;
    padding: 0 10px;
}

.matheditor-tab-content.active {
    display: flex;
}

/* Perfect fit layout - no scrolling */
.matheditor-tab-content.active {
    justify-content: space-evenly;
    align-items: flex-start;
}

/* Ensure all groups fit perfectly */
.matheditor-tab-content.active .matheditor-button-group {
    flex: 1 1 0;
    min-width: 80px;
    max-width: 160px;
}

/* Custom scrollbar for horizontal scrolling */
.matheditor-tab-content::-webkit-scrollbar {
    height: 6px;
}

.matheditor-tab-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.matheditor-tab-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.matheditor-tab-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Perfect Fit Word 365 Style Button Groups */
.matheditor-button-group {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    margin: 0 2px;
    padding: 8px 4px;
    position: relative;
    border: 1px solid transparent;
    border-radius: 4px;
    background: transparent;
    height: 78px;
    transition: all 0.2s ease;
    flex: 1 1 auto;
    white-space: nowrap;
    min-width: 80px;
    max-width: 160px;
}

.matheditor-button-group:hover {
    border-color: rgba(16, 110, 190, 0.3);
    background: rgba(16, 110, 190, 0.08);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.matheditor-button-group:hover .matheditor-group-label {
    color: #106ebe;
    font-weight: 600;
}

/* Flexible Button Row Container */
.matheditor-button-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 2px;
    margin-bottom: 6px;
    width: 100%;
    min-height: 32px;
}

/* Group Divider - Perfect Style */
.matheditor-group-divider {
    width: 1px;
    height: 78px;
    background: #e1e1e1;
    margin: 0 4px;
    flex-shrink: 0;
    align-self: stretch;
}

/* Flexible Group Label */
.matheditor-group-label {
    display: block;
    font-size: 9px;
    color: #605e5c;
    text-align: center;
    margin-top: 4px;
    font-weight: 500;
    text-transform: none;
    letter-spacing: 0.2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-top: 1px solid #f0f0f0;
    padding-top: 4px;
    width: 100%;
    line-height: 1.2;
}

/* Remove tooltip since we have visible labels */
.matheditor-button-group:hover::after {
    display: none;
}

/* Perfect Word 365 Style Ribbon Buttons */
.matheditor-toolbar-btn {
    padding: 4px 6px;
    border: 1px solid transparent;
    border-radius: 3px;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.15s ease;
    min-width: 26px;
    height: 26px;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 1px;
    color: #323130;
    box-shadow: none;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    vertical-align: top;
}

/* Perfect hover effects for better UX */
.matheditor-toolbar-btn:hover {
    background: rgba(16, 110, 190, 0.1);
    border-color: rgba(16, 110, 190, 0.3);
    color: #106ebe;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

/* Style for FontAwesome icons in buttons */
.matheditor-toolbar-btn i {
    font-size: 14px;
    color: #555;
    transition: all 0.2s ease;
}

/* Style for text next to icons */
.matheditor-toolbar-btn .btn-text {
    margin-left: 4px;
    font-size: 13px;
}

.matheditor-toolbar-btn:hover i {
    color: #106ebe;
}

.matheditor-toolbar-btn:active {
    background: rgba(16, 110, 190, 0.15);
    border-color: #106ebe;
    color: #106ebe;
    transform: translateY(0);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Focus state for accessibility */
.matheditor-toolbar-btn:focus {
    outline: 2px solid #106ebe;
    outline-offset: 1px;
    background: rgba(16, 110, 190, 0.1);
}

/* Remove old pulse animation */
.matheditor-toolbar-btn:hover::before {
    display: none;
}

.matheditor-toolbar-btn.large {
    padding: 4px 6px;
    font-size: 13px;
    min-width: 28px;
    height: 28px;
    font-weight: 400;
}

.matheditor-toolbar-btn.large i {
    font-size: 13px;
}

/* No longer using category headers */

.matheditor-buttons-container {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 16px;
    padding: 16px 0 0;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    flex-shrink: 0;
}

.matheditor-cancel-btn {
    padding: 8px 16px;
    border: 1px solid #8a8886;
    border-radius: 2px;
    background: #ffffff;
    cursor: pointer;
    font-size: 14px;
    color: #323130;
    font-weight: 400;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.matheditor-cancel-btn:hover {
    background: #f3f2f1;
    border-color: #605e5c;
    color: #323130;
    transform: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

.matheditor-cancel-btn:active {
    background: #edebe9;
    border-color: #323130;
    transform: none;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.matheditor-insert-btn {
    padding: 8px 20px;
    border: 1px solid #106ebe;
    border-radius: 2px;
    background: #106ebe;
    color: #ffffff;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.matheditor-insert-btn:hover {
    background: #005a9e;
    border-color: #005a9e;
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.matheditor-insert-btn:active {
    background: #004578;
    border-color: #004578;
    transform: none;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Remove shine effect for Office style */
.matheditor-insert-btn::after {
    display: none;
}

.matheditor-insert-btn:hover::after {
    display: none;
}

/* Hide MathLive virtual keyboard toggle button */
.ML__virtual-keyboard-toggle,
[part="virtual-keyboard-toggle"],
[data-ml__tooltip="Toggle Virtual Keyboard"],
[data-command="&quot;toggleVirtualKeyboard&quot;"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
}

math-field::part(virtual-keyboard-toggle) {
    display: none !important;
}
