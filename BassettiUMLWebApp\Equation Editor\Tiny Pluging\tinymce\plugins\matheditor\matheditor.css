/* Math Editor Dialog Styles */
.matheditor-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Arial, sans-serif;
}

.matheditor-content {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 1200px;
    min-width: 1000px;
    max-height: 90vh;
    min-height: 600px;
    overflow: hidden;
    padding: 0 0 10px 0;
    position: relative;
    display: flex;
    flex-direction: column;
}

.matheditor-container {
    padding: 0 20px;
    margin-top: 10px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.matheditor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    border-bottom: 1px solid #d1d1d1;
    padding: 16px 24px;
    background: #ffffff;
    color: #323130;
    height: 60px;
    border-radius: 4px 4px 0 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
}

.matheditor-header h2 {
    margin: 0;
    font-size: 18px;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    color: #323130;
    display: flex;
    align-items: center;
    gap: 8px;
}

.matheditor-header h2::before {
    content: "∑";
    font-size: 20px;
    color: #0078d4;
    font-weight: bold;
}

.matheditor-close-btn {
    background: transparent;
    border: 1px solid transparent;
    font-size: 16px;
    cursor: pointer;
    color: #605e5c;
    padding: 4px;
    line-height: 1;
    border-radius: 3px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.matheditor-close-btn:hover {
    background: rgba(16, 110, 190, 0.1);
    border-color: rgba(16, 110, 190, 0.3);
    color: #106ebe;
}

.matheditor-input-container {
    margin-bottom: 16px;
    margin-top: 6px;
    padding: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.matheditor-label {
    display: block;
    margin-bottom: 4px;
    font-weight: bold;
    color: #555;
    font-size: 12px;
    flex-shrink: 0;
}

#matheditor-input {
    width: 100%;
    font-size: 16px;
    border: 2px solid #2196f3;
    border-radius: 4px;
    min-height: 120px;
    flex: 1;
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.15);
    background-color: #f9f9f9;
    transition: all 0.3s;
    margin-bottom: 10px;
    resize: none;
}

/* Microsoft Word 365 Style Toolbar Tabs */
.matheditor-toolbar-tabs {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 0;
    border-bottom: 1px solid #d1d1d1;
    padding: 0;
    background: #f8f9fa;
    border-radius: 0;
    overflow-x: auto;
    overflow-y: hidden;
    height: 36px;
    box-shadow: none;
    position: relative;
    z-index: 1;
    flex-shrink: 0;
}

.matheditor-toolbar-tabs::-webkit-scrollbar {
    height: 2px;
}

.matheditor-toolbar-tabs::-webkit-scrollbar-thumb {
    background-color: rgba(96, 94, 92, 0.3);
    border-radius: 1px;
}

.matheditor-tab-btn {
    padding: 0 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 13px;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    transition: all 0.15s ease;
    margin: 0;
    color: #605e5c;
    position: relative;
    height: 36px;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 0;
    white-space: nowrap;
}

.matheditor-tab-btn:hover {
    color: #323130;
    background: rgba(96, 94, 92, 0.08);
}

.matheditor-tab-btn:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: transparent;
    transition: all 0.15s ease;
}

.matheditor-tab-btn:hover:after {
    background: rgba(0, 120, 212, 0.4);
}

.matheditor-tab-btn.active {
    background: #ffffff;
    color: #0078d4;
    font-weight: 600;
    z-index: 2;
    border-bottom: 3px solid #0078d4;
}

.matheditor-tab-btn.active:after {
    background: #0078d4;
}

/* Microsoft Word 365 Style Ribbon Toolbar */
.matheditor-toolbar {
    display: block;
    margin-bottom: 16px;
    padding: 16px 24px;
    background: #ffffff;
    border: 1px solid #d1d1d1;
    border-top: none;
    border-radius: 0;
    height: auto;
    min-height: 120px;
    max-height: 130px;
    overflow-x: hidden;
    overflow-y: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    position: relative;
    z-index: 1;
    width: 100%;
}

.matheditor-tab-content {
    display: none;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 8px;
    align-items: flex-start;
    justify-content: flex-start;
    height: 88px;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    width: 100%;
    padding: 0 8px;
}

.matheditor-tab-content.active {
    display: flex;
}

/* Responsive layout for better fit */
.matheditor-tab-content.active {
    justify-content: flex-start;
    align-items: flex-start;
}

/* Flexible group sizing */
.matheditor-tab-content.active .matheditor-button-group {
    flex: 0 0 auto;
    min-width: 85px;
    max-width: 140px;
}

/* Custom scrollbar for horizontal scrolling */
.matheditor-tab-content::-webkit-scrollbar {
    height: 4px;
}

.matheditor-tab-content::-webkit-scrollbar-track {
    background: #f3f2f1;
    border-radius: 2px;
}

.matheditor-tab-content::-webkit-scrollbar-thumb {
    background: #c8c6c4;
    border-radius: 2px;
}

.matheditor-tab-content::-webkit-scrollbar-thumb:hover {
    background: #a19f9d;
}

/* Microsoft Word 365 Style Button Groups */
.matheditor-button-group {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    margin: 0 4px;
    padding: 8px 6px;
    position: relative;
    border: 1px solid transparent;
    border-radius: 2px;
    background: transparent;
    height: 80px;
    transition: all 0.15s ease;
    flex: 0 0 auto;
    white-space: nowrap;
    min-width: 85px;
    max-width: 140px;
}

.matheditor-button-group:hover {
    border-color: rgba(0, 120, 212, 0.2);
    background: rgba(0, 120, 212, 0.05);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
    transform: none;
}

.matheditor-button-group:hover .matheditor-group-label {
    color: #0078d4;
    font-weight: 500;
}

/* Button Row Container */
.matheditor-button-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 1px;
    margin-bottom: 8px;
    width: 100%;
    min-height: 34px;
}

/* Group Divider */
.matheditor-group-divider {
    width: 1px;
    height: 80px;
    background: #d1d1d1;
    margin: 0 6px;
    flex-shrink: 0;
    align-self: stretch;
}

/* Group Label */
.matheditor-group-label {
    display: block;
    font-size: 10px;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    color: #605e5c;
    text-align: center;
    margin-top: 6px;
    font-weight: 400;
    text-transform: none;
    letter-spacing: 0.1px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-top: 1px solid #edebe9;
    padding-top: 4px;
    width: 100%;
    line-height: 1.1;
}

/* Remove tooltip since we have visible labels */
.matheditor-button-group:hover::after {
    display: none;
}

/* Microsoft Word 365 Style Ribbon Buttons */
.matheditor-toolbar-btn {
    padding: 4px 6px;
    border: 1px solid transparent;
    border-radius: 2px;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    transition: all 0.1s ease;
    min-width: 28px;
    height: 28px;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 1px;
    color: #323130;
    box-shadow: none;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    vertical-align: top;
}

/* Word 365 hover effects */
.matheditor-toolbar-btn:hover {
    background: rgba(0, 120, 212, 0.08);
    border-color: rgba(0, 120, 212, 0.2);
    color: #0078d4;
    box-shadow: none;
    transform: none;
}

/* Style for FontAwesome icons in buttons */
.matheditor-toolbar-btn i {
    font-size: 14px;
    color: #605e5c;
    transition: all 0.1s ease;
}

/* Style for text next to icons */
.matheditor-toolbar-btn .btn-text {
    margin-left: 4px;
    font-size: 12px;
}

.matheditor-toolbar-btn:hover i {
    color: #0078d4;
}

.matheditor-toolbar-btn:active {
    background: rgba(0, 120, 212, 0.12);
    border-color: rgba(0, 120, 212, 0.3);
    color: #0078d4;
    transform: none;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.08);
}

/* Focus state for accessibility */
.matheditor-toolbar-btn:focus {
    outline: 2px solid #0078d4;
    outline-offset: 1px;
    background: rgba(0, 120, 212, 0.08);
}

/* Remove old pulse animation */
.matheditor-toolbar-btn:hover::before {
    display: none;
}

.matheditor-toolbar-btn.large {
    padding: 6px 8px;
    font-size: 13px;
    min-width: 32px;
    height: 32px;
    font-weight: 400;
}

.matheditor-toolbar-btn.large i {
    font-size: 15px;
}

/* Action buttons container */
.matheditor-buttons-container {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 16px;
    padding: 16px 0 0;
    border-top: 1px solid #edebe9;
    flex-shrink: 0;
}

.matheditor-cancel-btn {
    padding: 8px 20px;
    border: 1px solid #8a8886;
    border-radius: 2px;
    background: #ffffff;
    cursor: pointer;
    font-size: 14px;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    color: #323130;
    font-weight: 400;
    transition: all 0.15s ease;
    position: relative;
    overflow: hidden;
}

.matheditor-cancel-btn:hover {
    background: #f3f2f1;
    border-color: #605e5c;
    color: #323130;
    transform: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

.matheditor-cancel-btn:active {
    background: #edebe9;
    border-color: #323130;
    transform: none;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.08);
}

.matheditor-insert-btn {
    padding: 8px 24px;
    border: 1px solid #0078d4;
    border-radius: 2px;
    background: #0078d4;
    color: #ffffff;
    cursor: pointer;
    font-size: 14px;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    transition: all 0.15s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.matheditor-insert-btn:hover {
    background: #106ebe;
    border-color: #106ebe;
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.matheditor-insert-btn:active {
    background: #005a9e;
    border-color: #005a9e;
    transform: none;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
}

/* Remove shine effect for Office style */
.matheditor-insert-btn::after {
    display: none;
}

.matheditor-insert-btn:hover::after {
    display: none;
}

/* Hide MathLive virtual keyboard toggle button */
.ML__virtual-keyboard-toggle,
[part="virtual-keyboard-toggle"],
[data-ml__tooltip="Toggle Virtual Keyboard"],
[data-command="&quot;toggleVirtualKeyboard&quot;"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
}

math-field::part(virtual-keyboard-toggle) {
    display: none !important;
}
